use datafusion::common::Result;
use jni::{
    objects::{GlobalRef, JValue},
    sys::{JNI_FALSE, JNI_TRUE},
    JNIEnv,
};
use once_cell::sync::OnceCell;

pub mod jni_bridge;

pub fn is_jni_bridge_inited() -> bool {
    jni_bridge::JavaClasses::inited()
}

pub fn is_task_running() -> bool {
    fn is_task_running_impl() -> Result<bool> {
        if !jni_call_static!(JniBridge.isTaskRunning() -> bool).unwrap() {
            jni_exception_clear!()?;
            return Ok(false);
        }
        Ok(true)
    }
    if !is_jni_bridge_inited() {
        // only for testing
        return true;
    }
    is_task_running_impl().expect("calling JniBridge.isTaskRunning() error")
}

pub fn java_true() -> &'static GlobalRef {
    static OBJ_TRUE: OnceCell<GlobalRef> = OnceCell::new();
    OBJ_TRUE.get_or_init(|| {
        let true_local = jni_new_object!(JavaBoolean(JNI_TRUE)).unwrap();
        jni_new_global_ref!(true_local.as_obj()).unwrap()
    })
}

pub fn java_false() -> &'static GlobalRef {
    static OBJ_FALSE: OnceCell<GlobalRef> = OnceCell::new();
    OBJ_FALSE.get_or_init(|| {
        let false_local = jni_new_object!(JavaBoolean(JNI_FALSE)).unwrap();
        jni_new_global_ref!(false_local.as_obj()).unwrap()
    })
}


pub fn get_double_array(env: &mut JNIEnv, array: jni::sys::jdoubleArray) -> Option<Vec<f64>> {
    if array.is_null() {
        return None;
    }

    let raw_array = array;
    let length = match env.get_array_length(raw_array) {
        Ok(len) => len as usize,
        Err(_) => return None,
    };

    if length == 0 {
        return Some(Vec::new());
    }

    let mut buffer = vec![0.0; length];
    match env.get_double_array_region(raw_array, 0, &mut buffer) {
        Ok(_) => Some(buffer),
        Err(_) => None,
    }
}


pub fn create_big_decimal(env: &mut JNIEnv, value: f64) -> jni::sys::jobject {
    let value_str = value.to_string();
    let j_string = env
        .new_string(value_str)
        .expect("Failed to create Java string");

    let big_decimal_class = env
        .find_class("java/math/BigDecimal")
        .expect("Failed to find BigDecimal class");

    let result = env
        .new_object(
            big_decimal_class,
            "(Ljava/lang/String;)V",
            &[JValue::Object(j_string.into())],
        )
        .expect("Failed to create BigDecimal object");

    result.into_raw()
}
