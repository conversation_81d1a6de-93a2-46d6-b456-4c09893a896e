use common::utils::stats::calculate_sum;
use jni::objects::{JClass, JObject, JString, JValue};
use jni::sys::{jboolean, jdouble, jdoubleArray, jobject, jstring};
use jni::JNIEnv;
use jni_bridge::jni_bridge::JavaClasses;
use jni_bridge::{create_big_decimal, get_double_array, jni_new_global_ref};

use std::str::FromStr;

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_sum(
    mut env: JNIEnv,
    _class: JClass,
    values: jdoubleArray,
) -> jobject {
    let result = get_double_array(&mut env, values).and_then(|arr| calculate_sum(&arr));
    match result {
        Some(value) => create_big_decimal(&mut env, value),
        None => std::ptr::null_mut(),
    }
}
